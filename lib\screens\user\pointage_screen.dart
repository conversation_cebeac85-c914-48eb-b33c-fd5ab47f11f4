import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';

import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../services/api_service.dart';
import '../../services/location_service.dart';
import '../../widgets/common/index.dart';

class PointageScreen extends StatefulWidget {
  const PointageScreen({super.key});

  @override
  State<PointageScreen> createState() => _PointageScreenState();
}

class _PointageScreenState extends State<PointageScreen> {
  final ApiService _apiService = ApiService();
  final LocationService _locationService = LocationService();
  
  Position? _currentPosition;
  Site? _assignedSite;
  Pointage? _activePointage;
  LocationCheckResponse? _locationCheck;

  bool _isLoadingLocation = false;
  bool _isProcessingPointage = false;
  bool _isLocationValid = false;
  bool _isWithinRange = false;
  double? _distanceToSite;
  
  Timer? _locationTimer;
  Timer? _clockTimer;
  
  String _currentTime = '';

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    _startClockTimer();
  }

  @override
  void dispose() {
    _locationTimer?.cancel();
    _clockTimer?.cancel();
    super.dispose();
  }

  void _initializeScreen() {
    _loadUserData();
    _checkCurrentLocation();
    _startLocationUpdates();
  }

  void _startClockTimer() {
    _updateCurrentTime();
    _clockTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateCurrentTime();
    });
  }

  void _updateCurrentTime() {
    setState(() {
      _currentTime = DateFormat('HH:mm:ss').format(DateTime.now());
    });
  }

  void _loadUserData() async {
    try {
      final user = context.read<AuthProvider>().user;
      if (user != null) {
        debugPrint('Loading data for user: ${user.name} (ID: ${user.id})');
        debugPrint('User defaultSiteId: ${user.defaultSiteId}');
        debugPrint('User email: ${user.email}');
        debugPrint('User role: ${user.role}');

        // Load assigned site for the current user using the new method
        Site? assignedSite;

        // Utiliser la nouvelle méthode getUserAssignedSite qui fonctionne avec l'endpoint check-location
        try {
          debugPrint('🔍 Getting user assigned site via check-location endpoint...');
          assignedSite = await _apiService.getUserAssignedSite();

          if (assignedSite != null) {
            debugPrint('✅ Assigned site found: ${assignedSite.name} (ID: ${assignedSite.id})');
          } else {
            debugPrint('❌ No assigned site found for user ${user.id}');
          }
        } catch (e) {
          debugPrint('❌ Error getting user assigned site: $e');
        }

        if (assignedSite != null) {
          setState(() {
            _assignedSite = assignedSite;
            _updateLocationStatus();
          });
        } else {
          debugPrint('No assigned site found for user ${user.id}');
          // Show error message to user
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لم يتم تخصيص موقع لك. يرجى التواصل مع الإدارة.'),
                backgroundColor: AppColors.warning,
              ),
            );
          }
        }

        // Ne pas charger automatiquement l'état du pointage actif
        // L'état sera déterminé uniquement par les actions manuelles de l'employé
        debugPrint('🔒 Skipping automatic active pointage loading to prevent unwanted button state changes');
        debugPrint('🎯 Button state will only change through manual employee actions');
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في تحميل بيانات المستخدم');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _startLocationUpdates() {
    _locationTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkCurrentLocation();
    });
  }

  void _checkCurrentLocation() async {
    if (_isLoadingLocation) return;
    
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final position = await _locationService.getCurrentPosition();
      setState(() {
        _currentPosition = position;
        _updateLocationStatus();
      });

      if (_assignedSite != null) {
        await _verifyLocationWithBackend();
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في الحصول على الموقع');
      _showErrorSnackBar(errorMessage);
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _verifyLocationWithBackend() async {
    if (_currentPosition == null || _assignedSite == null) {
      debugPrint('❌ Cannot verify location: position=${_currentPosition != null}, site=${_assignedSite != null}');
      return;
    }

    try {
      debugPrint('🔍 Verifying location with site ${_assignedSite!.name} (ID: ${_assignedSite!.id})');

      final request = LocationCheckRequest(
        // Ne pas passer siteId car l'API détermine automatiquement le site assigné
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      final response = await _apiService.checkLocation(request);  // Utiliser checkLocation au lieu de verifyLocation

      debugPrint('✅ Location check response: inRange=${response.inRange}, distance=${response.distance}m');

      setState(() {
        _locationCheck = response;
        _isLocationValid = response.inRange;
      });
    } catch (e) {
      debugPrint('❌ Error verifying location: $e');
      setState(() {
        _isLocationValid = false;
      });

      // Afficher l'erreur à l'utilisateur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحقق من الموقع: ${_extractErrorMessage(e, 'خطأ في التحقق من الموقع')}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _processPointage() async {
    if (_isProcessingPointage || _currentPosition == null) return;

    setState(() {
      _isProcessingPointage = true;
    });

    try {
      // Valider et nettoyer les données avant l'envoi
      final latitude = _currentPosition!.latitude;
      final longitude = _currentPosition!.longitude;
      final accuracy = _currentPosition!.accuracy;

      // Utiliser l'heure locale exacte (heure de l'Algérie UTC+1)
      // Ajouter 1 heure pour correspondre à la timezone Algérie
      final localTime = DateTime.now();
      final algeriaTime = localTime.add(const Duration(hours: 1));
      final timestamp = algeriaTime.millisecondsSinceEpoch ~/ 1000;

      // Double vérification du timestamp Algérie
      final verificationTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      debugPrint('🔍 TIMESTAMP VERIFICATION (ALGÉRIE):');
      debugPrint('  - Local Time: ${localTime.toString()}');
      debugPrint('  - Algeria Time: ${algeriaTime.toString()}');
      debugPrint('  - Converted back: ${verificationTime.toString()}');
      debugPrint('  - Match Algeria: ${algeriaTime.difference(verificationTime).inSeconds == 0}');

      debugPrint('🕐 TIMESTAMP DEBUGGING - CLIENT SIDE (ALGÉRIE):');
      debugPrint('  - Local DateTime: ${localTime.toString()}');
      debugPrint('  - Algeria DateTime: ${algeriaTime.toString()}');
      debugPrint('  - Algeria Time (HH:mm:ss): ${algeriaTime.hour.toString().padLeft(2, '0')}:${algeriaTime.minute.toString().padLeft(2, '0')}:${algeriaTime.second.toString().padLeft(2, '0')}');
      debugPrint('  - Timestamp (seconds): $timestamp');
      debugPrint('  - Timestamp (milliseconds): ${algeriaTime.millisecondsSinceEpoch}');
      debugPrint('  - ISO String: ${algeriaTime.toIso8601String()}');
      debugPrint('🇩🇿 TIMEZONE ALGÉRIE: UTC+1 (ajouté 1 heure)');
      debugPrint('🎯 TIMESTAMP OBLIGATOIRE: $timestamp');
      debugPrint('🚨 BACKEND: Carbon::createFromTimestamp($timestamp, "Africa/Algiers")');

      debugPrint('🔍 Creating pointage request with:');
      debugPrint('  - Latitude: $latitude');
      debugPrint('  - Longitude: $longitude');
      debugPrint('  - Accuracy: $accuracy');

      // Valider que les coordonnées sont valides
      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        throw Exception('Coordonnées GPS invalides');
      }

      // Valider l'accuracy (utiliser une valeur par défaut si invalide)
      final validAccuracy = accuracy > 0 ? accuracy : 10.0;

      final request = PointageCreateRequest(
        latitude: latitude,
        longitude: longitude,
        timestamp: timestamp, // Obligatoire maintenant
        accuracy: validAccuracy,
        exactTime: algeriaTime.toIso8601String(), // Heure exacte Algérie pour vérification
      );

      // Validation finale de la requête
      debugPrint('🚀 FINAL REQUEST VALIDATION (ALGÉRIE):');
      debugPrint('  - Latitude: $latitude');
      debugPrint('  - Longitude: $longitude');
      debugPrint('  - Timestamp: $timestamp');
      debugPrint('  - Accuracy: $validAccuracy');
      debugPrint('  - ExactTime: ${algeriaTime.toIso8601String()}');
      debugPrint('  - Request JSON: ${request.toJson()}');

      debugPrint('🚀 Sending pointage request: ${request.toJson()}');
      final response = await _apiService.savePointage(request);
      debugPrint('✅ Pointage response received: ${response.type}, isCheckIn: ${response.isCheckIn}');

      // Vérifier l'heure retournée par le backend
      debugPrint('🕐 TIMESTAMP VERIFICATION - BACKEND RESPONSE:');
      final backendTime = response.pointage.debutPointage;
      debugPrint('  - Backend DateTime: ${backendTime.toString()}');
      debugPrint('  - Backend Time (HH:mm:ss): ${backendTime.hour.toString().padLeft(2, '0')}:${backendTime.minute.toString().padLeft(2, '0')}:${backendTime.second.toString().padLeft(2, '0')}');
      final timeDifference = backendTime.difference(localTime).inSeconds;
      debugPrint('  - Time difference: $timeDifference seconds');

      // Alerte si l'heure est modifiée
      if (timeDifference.abs() > 2) {
        debugPrint('🚨 PROBLÈME DÉTECTÉ: L\'heure a été modifiée par le backend !');
        debugPrint('🔧 SOLUTION: Appliquer la correction dans PointageController.php');
      } else {
        debugPrint('✅ EXCELLENT: L\'heure exacte a été préservée !');
        debugPrint('🎯 Précision: ${timeDifference.abs()} seconde(s) de différence');
      }

      // Validation finale de la précision
      if (timeDifference == 0) {
        debugPrint('🏆 PERFECTION ATTEINTE: Synchronisation parfaite client-serveur !');
        debugPrint('🎯 OBJECTIF ACCOMPLI: Heure exacte préservée sans altération');
        debugPrint('✅ SYSTÈME PRÊT: Production avec précision absolue');
      } else if (timeDifference.abs() <= 1) {
        debugPrint('✅ EXCELLENT: Précision quasi-parfaite (${timeDifference.abs()}s)');
      }

      if (response.pointage.finPointage != null) {
        final backendEndTime = response.pointage.finPointage!;
        debugPrint('  - Backend End DateTime: ${backendEndTime.toString()}');
        debugPrint('  - Backend End Time (HH:mm:ss): ${backendEndTime.hour.toString().padLeft(2, '0')}:${backendEndTime.minute.toString().padLeft(2, '0')}:${backendEndTime.second.toString().padLeft(2, '0')}');
        debugPrint('  - End time difference: ${backendEndTime.difference(localTime).inSeconds} seconds');
      }

      setState(() {
        if (response.isCheckIn) {
          // Check-in: set the new active pointage
          _activePointage = response.pointage;
          debugPrint('✅ Active pointage set: ID ${response.pointage.id}');
          debugPrint('🔄 Button should now show: إنهاء الحضور');
        } else {
          // Check-out: clear the active pointage immediately
          _activePointage = null;
          debugPrint('✅ Active pointage cleared (check-out completed)');
          debugPrint('🔄 Button should now show: بدء الحضور');
        }
        debugPrint('🎯 State updated - _activePointage: ${_activePointage?.id ?? 'null'}');
      });

      // Force un rebuild complet pour s'assurer que l'UI se met à jour
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            // Force rebuild
          });
        }
      });

      // Show enhanced success message with Algeria time
      final currentAlgeriaTime = DateTime.now().add(const Duration(hours: 1));
      final algeriaTimeStr = '${currentAlgeriaTime.hour.toString().padLeft(2, '0')}:${currentAlgeriaTime.minute.toString().padLeft(2, '0')}';

      if (response.isCheckIn) {
        _showSuccessSnackBar('✅ تم بدء الحضور في الساعة $algeriaTimeStr (توقيت الجزائر)');
      } else {
        _showCompletionSnackBar('✅ تم إنهاء الحضور في الساعة $algeriaTimeStr (توقيت الجزائر) - العملية مكتملة');
      }

      // Ne pas recharger les données automatiquement pour éviter les changements d'état non désirés
      // L'état du bouton doit rester stable après l'action manuelle
      debugPrint('🔒 Skipping automatic data reload to maintain button state stability');

    } catch (e) {
      debugPrint('❌ Error processing pointage: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في معالجة الحضور');
      _showErrorSnackBar(errorMessage);
    } finally {
      setState(() {
        _isProcessingPointage = false;
      });
    }
  }

  void _updateLocationStatus() {
    if (_currentPosition != null && _assignedSite != null) {
      _distanceToSite = _locationService.getDistanceToSite(_currentPosition!, _assignedSite!);
      _isWithinRange = _locationService.isWithinRange(_currentPosition!, _assignedSite!);
    } else {
      _distanceToSite = null;
      _isWithinRange = false;
    }
  }

  String _extractErrorMessage(dynamic error, String fallbackMessage) {
    if (error is ApiException) {
      return error.message;
    }
    return '$fallbackMessage: ${error.toString()}';
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showCompletionSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الحضور والانصراف'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkCurrentLocation,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Recharger seulement les données de localisation, pas l'état du pointage
          debugPrint('🔄 Manual refresh - updating location only, preserving button state');
          _checkCurrentLocation();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildTimeCard(),
              const SizedBox(height: 16),
              _buildLocationCard(),
              const SizedBox(height: 16),
              _buildSiteCard(),
              const SizedBox(height: 16),
              _buildActivePointageCard(),
              const SizedBox(height: 24),
              _buildPointageButton(),
              const SizedBox(height: 16),
              _buildStatusCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeCard() {
    return ModernCard(
      child: Column(
        children: [
          Icon(
            Icons.access_time,
            size: 48,
            color: AppColors.primaryBlue,
          ),
          const SizedBox(height: 8),
          Text(
            _currentTime,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            DateFormat('EEEE، d MMMM yyyy', 'ar').format(DateTime.now()),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isLocationValid ? Icons.location_on : Icons.location_off,
                color: _isLocationValid ? AppColors.success : AppColors.error,
              ),
              const SizedBox(width: 8),
              Text(
                'الموقع الحالي',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isLoadingLocation)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (_currentPosition != null) ...[
            Text(
              'خط العرض: ${_currentPosition!.latitude.toStringAsFixed(6)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'خط الطول: ${_currentPosition!.longitude.toStringAsFixed(6)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'الدقة: ${_currentPosition!.accuracy.toStringAsFixed(1)} متر',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ] else ...[
            Text(
              'جاري تحديد الموقع...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
          if (_locationCheck != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _locationCheck!.inRange
                    ? AppColors.success.withValues(alpha: 0.1)
                    : AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _locationCheck!.inRange ? Icons.check_circle : Icons.error,
                    color: _locationCheck!.inRange ? AppColors.success : AppColors.error,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _locationCheck!.inRange
                          ? 'داخل النطاق المسموح (${_locationCheck!.distance.toStringAsFixed(1)} متر)'
                          : 'خارج النطاق المسموح (${_locationCheck!.distance.toStringAsFixed(1)} متر)',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _locationCheck!.inRange ? AppColors.success : AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSiteCard() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: AppColors.primaryBlue,
              ),
              const SizedBox(width: 8),
              Text(
                'الموقع المخصص',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_assignedSite != null) ...[
            Text(
              _assignedSite!.name,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'خط العرض: ${_assignedSite!.latitude.toStringAsFixed(6)}، خط الطول: ${_assignedSite!.longitude.toStringAsFixed(6)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            if (_currentPosition != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    _isWithinRange ? Icons.location_on : Icons.location_off,
                    size: 16,
                    color: _isWithinRange ? AppColors.success : AppColors.error,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'المسافة: ${_distanceToSite?.toStringAsFixed(0) ?? '--'}م',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _isWithinRange ? AppColors.success : AppColors.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ] else ...[
            Text(
              'لم يتم تخصيص موقع',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivePointageCard() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _activePointage != null ? Icons.timer : Icons.timer_off,
                color: _activePointage != null ? AppColors.success : AppColors.textSecondary,
              ),
              const SizedBox(width: 8),
              Text(
                'الحضور الحالي',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_activePointage != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.play_circle,
                        color: AppColors.success,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'بدأ في: ${_activePointage!.displayStartTime}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الموقع: ${_activePointage!.siteName}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    'التاريخ: ${_activePointage!.displayDate}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: AppColors.success,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'جاهز لتسجيل حضور جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPointageButton() {
    debugPrint('🔄 _buildPointageButton called - rebuilding button widget');

    final bool canProcess = _currentPosition != null &&
                           _isLocationValid &&
                           !_isProcessingPointage &&
                           _assignedSite != null;

    final bool isCheckIn = _activePointage == null;

    // Debug logging for button state
    debugPrint('🔍 Button state debug:');
    debugPrint('  - _activePointage: ${_activePointage?.id ?? 'null'}');
    debugPrint('  - isCheckIn: $isCheckIn');
    debugPrint('  - _isProcessingPointage: $_isProcessingPointage');

    // Dynamic button text based on loading state and pointage status
    String buttonText;
    if (_isProcessingPointage) {
      buttonText = isCheckIn ? 'جاري بدء الحضور...' : 'جاري إنهاء الحضور...';
    } else {
      buttonText = isCheckIn ? 'بدء الحضور' : 'إنهاء الحضور';
    }

    debugPrint('  - buttonText: $buttonText');

    return ActionButton(
      text: buttonText,
      onPressed: canProcess ? _processPointage : null,
      variant: isCheckIn ? ActionButtonVariant.primary : ActionButtonVariant.secondary,
      size: ActionButtonSize.large,
      isLoading: _isProcessingPointage,
      icon: isCheckIn ? Icons.login : Icons.logout,
    );
  }

  Widget _buildStatusCard() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة النظام',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildStatusItem(
            'الموقع',
            _currentPosition != null ? 'متاح' : 'غير متاح',
            _currentPosition != null ? AppColors.success : AppColors.error,
            _currentPosition != null ? Icons.check_circle : Icons.error,
          ),
          _buildStatusItem(
            'النطاق المسموح',
            _isLocationValid ? 'داخل النطاق' : 'خارج النطاق',
            _isLocationValid ? AppColors.success : AppColors.error,
            _isLocationValid ? Icons.check_circle : Icons.error,
          ),
          _buildStatusItem(
            'الموقع المخصص',
            _assignedSite != null ? 'محدد' : 'غير محدد',
            _assignedSite != null ? AppColors.success : AppColors.warning,
            _assignedSite != null ? Icons.check_circle : Icons.warning,
          ),
          _buildStatusItem(
            'الحضور النشط',
            _activePointage != null ? 'نشط' : 'غير نشط',
            _activePointage != null ? AppColors.success : AppColors.textSecondary,
            _activePointage != null ? Icons.timer : Icons.timer_off,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
